<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

  <changeSet author="suryansh.purwar" id="yaqeen-booking-agreement-no-column-add">
    <addColumn tableName="booking" nullable="true">
      <column name="agreement_no" type="VARCHAR(20)"/>
    </addColumn>
    <sqlFile path="/db/data/booking-yaqeen-agreement-no-populate.sql"/>
    <createIndex tableName="booking" indexName="idx_booking_agreement_no" unique="true">
      <column name="agreement_no"/>
    </createIndex>
  </changeSet>
</databaseChangeLog>